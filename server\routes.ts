import type { Express } from "express";
import { createServer, type Server } from "http";
import { mockClientDetails } from "./data/clientDetails";
import { dashboardStats, quickActions, recentActivities } from "./data/dashboard";
import { mockRequests, requestStats } from "./data/requests";
import { storage } from "./storage";

export async function registerRoutes(app: Express): Promise<Server> {
  // Dashboard API endpoints
  app.get("/api/dashboard/stats", (_req, res) => {
    res.json(dashboardStats);
  });

  app.get("/api/dashboard/activities", (_req, res) => {
    res.json(recentActivities);
  });

  app.get("/api/dashboard/quick-actions", (_req, res) => {
    res.json(quickActions);
  });

  // Clients API endpoints
  app.get("/api/clients", async (_req, res) => {
    try {
      console.log("📡 /api/clients endpoint hit");

      // Try database first, fallback to mock data if it fails
      try {
        console.log("🔍 Attempting to fetch from database...");
        const clients = await storage.getClients();
        console.log("✅ Database fetch successful, found", clients.length, "clients");
        res.json(clients);
      } catch (dbError) {
        console.warn("⚠️ Database fetch failed, using mock data:", dbError.message);

        // Fallback to mock data
        const mockData = [
          {
            id: 1,
            name: "Test Client 1",
            sector: "Technology",
            years_in_business: 5,
            status: "Active",
            registration_date: "2024-01-15",
            current_balance: "50000.00",
            inflow: "5000.00",
            outflow: "3000.00"
          },
          {
            id: 2,
            name: "Test Client 2",
            sector: "Retail",
            years_in_business: 3,
            status: "Active",
            registration_date: "2024-03-20",
            current_balance: "25000.00",
            inflow: "3000.00",
            outflow: "2000.00"
          }
        ];

        console.log("📤 Sending mock clients data");
        res.json(mockData);
      }
    } catch (error) {
      console.error("❌ Error in /api/clients:", error);
      res.status(500).json({ error: "Failed to fetch clients" });
    }
  });

  app.get("/api/clients/:id", (req, res) => {
    const clientId = req.params.id;
    const client = mockClientDetails[clientId as keyof typeof mockClientDetails];

    if (!client) {
      return res.status(404).json({ error: "Client not found" });
    }

    res.json(client);
  });

  // Requests API endpoints
  app.get("/api/requests/stats", (_req, res) => {
    res.json(requestStats);
  });

  app.get("/api/requests", (_req, res) => {
    res.json(mockRequests);
  });

  const httpServer = createServer(app);

  return httpServer;
}
