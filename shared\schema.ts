import { integer, pgTable, serial, text, decimal, timestamp, date } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

export const clients = pgTable("clients", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  sector: text("sector"),
  years_in_business: integer("years_in_business"),
  status: text("status"),
  registration_date: date("registration_date"),
  collateral_loan_ratio: decimal("collateral_loan_ratio", { precision: 5, scale: 4 }),
  months_vat: integer("months_vat"),
  current_balance: decimal("current_balance", { precision: 15, scale: 2 }),
  avg_balance_6m: decimal("avg_balance_6m", { precision: 15, scale: 2 }),
  min_balance_3m: decimal("min_balance_3m", { precision: 15, scale: 2 }),
  inflow_outflow_ratio: decimal("inflow_outflow_ratio", { precision: 8, scale: 4 }),
  inflow: decimal("inflow", { precision: 15, scale: 2 }),
  outflow: decimal("outflow", { precision: 15, scale: 2 }),
  avg_pos: decimal("avg_pos", { precision: 15, scale: 2 }),
  pos_growth_3m: decimal("pos_growth_3m", { precision: 8, scale: 4 }),
  chargeback_rate: decimal("chargeback_rate", { precision: 5, scale: 4 }),
  repayment_missed_12m: integer("repayment_missed_12m"),
  finance_used_12m: decimal("finance_used_12m", { precision: 15, scale: 2 }),
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow(),
});

export type Client = typeof clients.$inferSelect;
export type InsertClient = typeof clients.$inferInsert;


